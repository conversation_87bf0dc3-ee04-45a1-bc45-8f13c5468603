import { useMemo } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
} from "@/components/ui/custom-card";
import { ClipboardPenLine, Contact, Users, UsersRound } from "lucide-react";
import { Team } from "@/types";
import { Badge } from "@/components/ui/badge";
import { useGameSessions } from "@/hooks/useGameSessions";
import { useTeams } from "@/hooks/useTeams";
import LoadingSpinner from "@/components/LoadingSpinner";
import { Separator } from "@/components/ui/separator";
import { useEffect, useState } from "react";
import { PlayerName } from "@/components/player-profile/PlayerName";

// Custom gradient backgrounds with gray tones
const TEAM_GRADIENTS = [
  "bg-gradient-to-br from-zinc-600 to-zinc-800", // Dark zinc
  "bg-gradient-to-br from-slate-500 to-slate-700", // Slate
  "bg-gradient-to-br from-gray-500 to-gray-700", // Gray
  "bg-gradient-to-br from-neutral-500 to-neutral-700", // Neutral
];

interface TeamDisplayProps {
  className?: string;
  isAdminView?: boolean;
}

export default function TeamDisplay({ className, isAdminView = false }: TeamDisplayProps) {
  const { currentSession, loading: sessionLoading } = useGameSessions({
    fetchCurrent: true,
    fetchPast: false,
  });
  const { teams, loading: teamsLoading } = useTeams(currentSession?.id);
  const [assignedTeam, setAssignedTeam] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<any>(null);

  // Function to get team gradient based on index
  const getTeamGradient = (index: number) => {
    return TEAM_GRADIENTS[index % TEAM_GRADIENTS.length];
  };

  useEffect(() => {
    const userId = localStorage.getItem("currentUserId");
    if (userId) {
      setCurrentUser({ id: userId });
    }
  }, []);

  useEffect(() => {
    if (teams && currentUser) {
      for (const team of teams) {
        const isInTeam = team.players.some((player) => player.id === currentUser.id);
        if (isInTeam) {
          setAssignedTeam(team.name);
          break;
        }
      }
    }
  }, [teams, currentUser]);

  if (sessionLoading || teamsLoading) {
    return (
      <Card className={`${className} dark:bg-zinc-900 dark:border-zinc-800`}>
        <CardHeader>
          <div className="flex items-center gap-2">
            <UsersRound className="h-5 w-5" />
            <CardTitle className="dark:text-white">Teams</CardTitle>
          </div>
          <CardDescription className="dark:text-zinc-400">
            Übersicht der generierten Teams und deren Spieler
          </CardDescription>
          <Separator className="dark:bg-zinc-800" />
        </CardHeader>
        <CardContent className="flex justify-center items-center py-12">
          <LoadingSpinner />
        </CardContent>
      </Card>
    );
  }

  if (!currentSession || !currentSession.isTeamGenerated || !teams) {
    return null;
  }

  return (
    <Card className={`${className} dark:bg-zinc-900 dark:border-zinc-800`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="min-w-0 flex-1">
            <CardTitle className="text-lg sm:text-xl dark:text-white truncate">
              Teams
            </CardTitle>
            <CardDescription className="text-xs sm:text-sm dark:text-zinc-400 truncate">
              Übersicht der generierten Teams und deren Spieler
            </CardDescription>
          </div>
        </div>
        <Separator className="dark:bg-zinc-800" />
      </CardHeader>
      <CardContent className="pt-3 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {teams.map((team, index) => {
            const isUserTeam = team.name === assignedTeam;
            return (
              <div
                key={team.id}
                className={`rounded-xl p-4 ${isUserTeam ? "ring-2 ring-amber-500 dark:ring-amber-400/70" : ""
                  } ${getTeamGradient(index)} text-white relative overflow-hidden`}
              >
                {isUserTeam && (
                  <div className="absolute top-2 right-2 bg-white dark:bg-zinc-800 text-amber-600 dark:text-amber-400 text-xs font-medium px-2 py-1 rounded-full">
                    Dein Team
                  </div>
                )}
                <div className="mb-3 flex justify-between items-center">
                  <h3 className="text-xl font-bold">{team.name}</h3>
                  {isAdminView && (
                    <Badge className="bg-white/20 text-white hover:bg-white/30">
                      Ø {team.averageRating?.toFixed(1)}
                    </Badge>
                  )}
                </div>
                <p className="text-white/80 text-sm mb-2">{team.players.length} Spieler</p>
                <ul className="space-y-2">
                  {team.players.map((player) => (
                    <li
                      key={player.id}
                      className={`${player.id === currentUser?.id
                          ? "font-bold bg-black/10 dark:bg-black/20"
                          : "font-normal"
                        } p-2 rounded-lg flex justify-between items-center`}
                    >
                      <div className="flex items-center gap-2">
                        <PlayerName
                          player={player}
                          playerName={player.name}
                          className={player.id === currentUser?.id ? "font-bold" : "font-normal"}
                        />
                        {player.jerseyNumber && (
                          <span className="text-xs bg-white/30 px-2 py-1 rounded-full">
                            #{player.jerseyNumber}
                          </span>
                        )}
                      </div>
                      {isAdminView && (
                        <span className="text-xs bg-white/30 px-2 py-1 rounded-full">
                          {player.rating}
                        </span>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
