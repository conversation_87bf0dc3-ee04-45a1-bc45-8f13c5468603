import React from "react";
import { Calendar, Hourglass, Timer, MapPin } from "lucide-react";
import { formatDate } from "@/utils/dateUtils";

interface GameInformationProps {
  nextGameDate: Date;
  timeToGame: string;
  displayDuration: string;
}

/**
 * Component for displaying general game information
 * Shows date, location, time remaining, and game duration
 */
export function GameInformation({
  nextGameDate,
  timeToGame,
  displayDuration,
}: GameInformationProps) {
  return (
    <div className="grid grid-cols-2 gap-2 sm:gap-3">
      {/* Game Time Info */}
      <div className="flex items-center gap-2 sm:gap-3 bg-gray-50 dark:bg-zinc-800 p-2 sm:p-3 rounded-lg">
        <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-full shadow-sm flex-shrink-0">
          <Calendar className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div className="min-w-0 flex-1">
          <p className="text-xs sm:text-sm font-semibold dark:text-white truncate">Datum & Uhrzeit:</p>
          <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 truncate">{formatDate(nextGameDate)}</p>
        </div>
      </div>

      {/* Location */}
      <div className="flex items-center gap-2 sm:gap-3 bg-gray-50 dark:bg-zinc-800 p-2 sm:p-3 rounded-lg">
        <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-full shadow-sm flex-shrink-0">
          <MapPin className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div className="min-w-0 flex-1">
          <p className="text-xs sm:text-sm font-semibold dark:text-white truncate">Spielort:</p>
          <a
            href="https://g.co/kgs/2b1T676"
            target="_blank"
            rel="noopener noreferrer"
            className="text-xs sm:text-sm text-blue-600 dark:text-blue-400 hover:underline truncate block"
            title="Soccerhalle Niederrhein"
          >
            Soccerhalle Niederrhein
          </a>
        </div>
      </div>

      {/* Time to Game */}
      <div className="flex items-center gap-2 sm:gap-3 bg-gray-50 dark:bg-zinc-800 p-2 sm:p-3 rounded-lg">
        <div className="bg-team-primary/20 dark:bg-gray-900/40 p-1.5 rounded-full shadow-sm flex-shrink-0">
          <Hourglass className="h-4 w-4 sm:h-5 sm:w-5 text-team-primary dark:text-gray-400/90" />
        </div>
        <div className="min-w-0 flex-1">
          <p className="text-xs sm:text-sm font-semibold dark:text-white truncate">Verbleibende Zeit:</p>
          <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 truncate">{timeToGame}</p>
        </div>
      </div>

      {/* Game Duration */}
      <div className="flex items-center gap-2 sm:gap-3 bg-gray-50 dark:bg-zinc-800 p-2 sm:p-3 rounded-lg">
        <div className="bg-green-100 dark:bg-green-900/30 p-1.5 rounded-full shadow-sm flex-shrink-0">
          <Timer className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 dark:text-green-400" />
        </div>
        <div className="min-w-0 flex-1">
          <p className="text-xs sm:text-sm font-semibold dark:text-white truncate">Spieldauer:</p>
          <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 truncate">{displayDuration}</p>
        </div>
      </div>
    </div>
  );
}
