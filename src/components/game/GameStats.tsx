import React from "react";
import { Users, Volleyball } from "lucide-react";
import { cn } from "@/lib/utils";
import LoadingSpinner from "@/components/LoadingSpinner";

interface GameStatsProps {
  inPlayersCount: number;
  hasEnoughPlayers: boolean;
  reservePlayers: number;
  loading: boolean;
}

/**
 * Component for displaying game player statistics
 * Shows player counts, game status, and reserve players
 */
export function GameStats({
  inPlayersCount,
  hasEnoughPlayers,
  reservePlayers,
  loading,
}: GameStatsProps) {
  if (loading) {
    return (
      <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-zinc-800 rounded-lg">
        <LoadingSpinner size="sm" />
        <span className="text-sm text-gray-500 dark:text-gray-400">Lade Spielerliste...</span>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 gap-2 sm:gap-3">
      <div className="flex items-start gap-2 sm:gap-3 bg-gray-50 dark:bg-zinc-800 p-2 sm:p-3 rounded-lg">
        <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-full shadow-sm flex-shrink-0 mt-0.5">
          <Users className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div className="min-w-0 flex-1">
          <p className="text-xs sm:text-sm font-semibold dark:text-white leading-tight">Anmeldungen:</p>
          <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 leading-tight break-words">{inPlayersCount} Spieler</p>
        </div>
      </div>

      <div className="flex items-start gap-2 sm:gap-3 bg-gray-50 dark:bg-zinc-800 p-2 sm:p-3 rounded-lg">
        <div
          className={cn(
            "p-1.5 rounded-full shadow-sm flex-shrink-0 mt-0.5",
            hasEnoughPlayers
              ? "bg-green-100 dark:bg-green-900/30"
              : "bg-amber-100 dark:bg-amber-900/30"
          )}
        >
          <Volleyball
            className={cn(
              "h-4 w-4 sm:h-5 sm:w-5",
              hasEnoughPlayers
                ? "text-green-600 dark:text-green-400"
                : "text-amber-600 dark:text-amber-400"
            )}
          />
        </div>
        <div className="min-w-0 flex-1">
          <p className="text-xs sm:text-sm font-semibold dark:text-white leading-tight">Status:</p>
          <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 leading-tight break-words">
            {hasEnoughPlayers ? "Spiel bestätigt" : "Noch nicht bestätigt"}
          </p>
        </div>
      </div>

      {reservePlayers > 0 && (
        <div className="flex items-start gap-2 sm:gap-3 bg-gray-50 dark:bg-zinc-800 p-2 sm:p-3 rounded-lg">
          <div className="bg-amber-100 dark:bg-amber-900/30 p-1.5 rounded-full shadow-sm flex-shrink-0 mt-0.5">
            <Users className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600 dark:text-amber-400" />
          </div>
          <div className="min-w-0 flex-1">
            <p className="text-xs sm:text-sm font-semibold dark:text-white leading-tight">Reservespieler:</p>
            <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 leading-tight break-words">{reservePlayers} Spieler</p>
          </div>
        </div>
      )}

      {!hasEnoughPlayers && (
        <div className="flex items-start gap-2 sm:gap-3 bg-gray-50 dark:bg-zinc-800 p-2 sm:p-3 rounded-lg">
          <div className="bg-amber-100 dark:bg-amber-900/30 p-1.5 rounded-full shadow-sm flex-shrink-0 mt-0.5">
            <Users className="h-4 w-4 sm:h-5 sm:w-5 text-amber-600 dark:text-amber-400" />
          </div>
          <div className="min-w-0 flex-1">
            <p className="text-xs sm:text-sm font-semibold dark:text-white leading-tight">Benötigt:</p>
            <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 leading-tight break-words">
              Noch {Math.max(10 - inPlayersCount, 0)} Spieler
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
